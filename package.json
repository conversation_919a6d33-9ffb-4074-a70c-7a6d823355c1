{"name": "@expo/expo", "version": "1.4.0", "private": true, "author": "Expo", "license": "MIT", "scripts": {"setup:docs": "./scripts/download-dependencies.sh", "setup:native": "./scripts/download-dependencies.sh && ./scripts/setup-react-android.sh", "postinstall": "yarn-deduplicate && yarn workspace @expo/cli prepare && patch-package && node ./tools/bin/expotools.js validate-workspace-dependencies", "lint": "eslint .", "tsc": "echo 'You are trying to run \"tsc\" in the workspace root. Run it from an individual package instead.' && exit 1", "android": "expo run:android", "ios": "expo run:ios"}, "workspaces": {"packages": ["apps/*", "packages/*", "packages/@expo/*"]}, "resolutions": {"react-native": "0.76.9", "react": "18.3.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/core": "7.0.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/elements": "2.2.5", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/routers": "7.0.0", "@react-navigation/stack": "^7.1.1", "react-native-screens": "~4.5.0", "**/util": "~0.12.4"}, "dependencies": {"eslint": "^8.57.1", "jsc-android": "^250231.0.0", "node-gyp": "^10.0.1", "patch-package": "^8.0.0", "prettier": "^3.3.3", "yarn-deduplicate": "^6.0.2", "react": "*", "react-native": "*", "react-native-windows": "*"}, "volta": {"node": "20.12.2"}}