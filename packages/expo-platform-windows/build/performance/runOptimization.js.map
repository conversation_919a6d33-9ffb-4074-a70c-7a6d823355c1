{"version": 3, "file": "runOptimization.js", "sourceRoot": "", "sources": ["../../src/performance/runOptimization.ts"], "names": [], "mappings": ";;AAEA;;;;;GAKG;;;AAIH,mDAAuD;AAEvD,KAAK,UAAU,IAAI;IACjB,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IAErE,kDAAkD;IAClD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAErD,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,mBAAmB;QACnB,MAAM,SAAS,GAAG,IAAI,oCAAoB,CAAC,WAAW,CAAC,CAAC;QACxD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAErD,UAAU;QACV,MAAM,oBAAoB,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,mBAAmB,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,CAAC,oBAAoB,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,GAAG,CAClE,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,4BAA4B,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,OAAO,CAAC,GAAG,CAAC,cAAc,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,GAAG,CAAC,CAAC;QAEhD,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CACT,mBAAmB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CACzF,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CACT,oBAAoB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAC3F,CAAC;QAEF,kBAAkB;QAClB,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACrC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;SACrE;QAED,uBAAuB;QACvB,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,mBAAmB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,YAAY,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,WAAW,IAAI,EAAE,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;SAChF;aAAM,IAAI,WAAW,IAAI,EAAE,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;SACnE;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;SACzE;QAED,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;KACpE;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC;AAkDgB,0CAA0B;AAhD3C;;GAEG;AACH,SAAS,QAAQ;IACf,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2Bb,CAAC,CAAC;AACH,CAAC;AAED,yBAAyB;AACzB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;IAC3B,uDAAuD;IACvD,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClE,QAAQ,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;CACJ"}