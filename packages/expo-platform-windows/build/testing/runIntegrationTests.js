#!/usr/bin/env node
"use strict";
/**
 * Integration Test Runner
 *
 * Runs comprehensive end-to-end integration tests for Windows platform
 * to validate the complete development workflow.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runIntegrationTests = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const integrationTesting_1 = require("./integrationTesting");
async function main() {
    console.log('🚀 EXPO PLATFORM WINDOWS - INTEGRATION TEST RUNNER\n');
    // Create temporary test directory
    const testProjectRoot = path.join(__dirname, '..', '..', 'temp-integration-tests');
    try {
        // Setup test environment
        console.log('🔧 Setting up integration test environment...');
        await setupIntegrationTestEnvironment(testProjectRoot);
        // Run integration tests
        console.log('🧪 Starting integration tests...\n');
        const tester = new integrationTesting_1.IntegrationTester(testProjectRoot);
        const results = await tester.runAllTests();
        // Summary
        const passedCount = results.filter((r) => r.passed).length;
        const totalCount = results.length;
        const successRate = Math.round((passedCount / totalCount) * 100);
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        console.log('🎯 INTEGRATION TEST RESULTS:');
        console.log(`   Tests run: ${totalCount}`);
        console.log(`   Tests passed: ${passedCount}`);
        console.log(`   Success rate: ${successRate}%`);
        console.log(`   Total duration: ${Math.round(totalDuration / 1000)}s`);
        if (successRate === 100) {
            console.log('   🎉 All integration tests passed!');
            console.log('   ✅ Windows platform is ready for production use');
        }
        else if (successRate >= 75) {
            console.log('   👍 Most integration tests passed');
            console.log('   ⚠️  Some issues need attention before production');
        }
        else {
            console.log('   ⚠️  Significant integration issues detected');
            console.log('   🔧 Platform needs additional work before production use');
        }
        // Show failed tests
        const failedTests = results.filter((r) => !r.passed);
        if (failedTests.length > 0) {
            console.log('\n❌ FAILED TESTS:');
            failedTests.forEach((test) => {
                console.log(`   - ${test.testName}`);
                test.errors.forEach((error) => console.log(`     Error: ${error}`));
            });
        }
        // Exit with appropriate code
        process.exit(successRate === 100 ? 0 : 1);
    }
    catch (error) {
        console.error('💥 Integration test runner failed:', error.message);
        process.exit(1);
    }
    finally {
        // Cleanup
        console.log('\n🧹 Cleaning up integration test environment...');
        await cleanupIntegrationTestEnvironment(testProjectRoot);
    }
}
exports.runIntegrationTests = main;
/**
 * Setup integration test environment
 */
async function setupIntegrationTestEnvironment(testRoot) {
    // Create test directory
    if (!fs.existsSync(testRoot)) {
        fs.mkdirSync(testRoot, { recursive: true });
    }
    // Validate environment
    await validateTestEnvironment();
    console.log(`   ✅ Integration test environment ready at: ${testRoot}`);
}
/**
 * Validate test environment has required tools
 */
async function validateTestEnvironment() {
    const requiredTools = [
        { name: 'Node.js', command: 'node --version' },
        { name: 'npm', command: 'npm --version' },
        { name: 'npx', command: 'npx --version' },
    ];
    console.log('   🔍 Validating test environment...');
    for (const tool of requiredTools) {
        try {
            const { execSync } = require('child_process');
            execSync(tool.command, { stdio: 'pipe' });
            console.log(`     ✅ ${tool.name} available`);
        }
        catch (error) {
            console.log(`     ❌ ${tool.name} not available`);
            throw new Error(`Required tool not found: ${tool.name}`);
        }
    }
    // Check for optional Windows development tools
    const optionalTools = [
        { name: 'MSBuild', command: 'where msbuild' },
        { name: 'Visual Studio', command: 'where devenv' },
    ];
    for (const tool of optionalTools) {
        try {
            const { execSync } = require('child_process');
            execSync(tool.command, { stdio: 'pipe' });
            console.log(`     ✅ ${tool.name} available (optional)`);
        }
        catch (error) {
            console.log(`     ⚠️  ${tool.name} not available (optional)`);
        }
    }
}
/**
 * Cleanup integration test environment
 */
async function cleanupIntegrationTestEnvironment(testRoot) {
    if (fs.existsSync(testRoot)) {
        try {
            // Remove test directory
            fs.rmSync(testRoot, { recursive: true, force: true });
            console.log('   ✅ Integration test environment cleaned up');
        }
        catch (error) {
            console.log(`   ⚠️  Failed to cleanup test environment: ${error.message}`);
        }
    }
}
/**
 * Show help information
 */
function showHelp() {
    console.log(`
Expo Platform Windows - Integration Test Runner

Usage:
  npm run test:integration     Run all integration tests
  npm run test:integration -- --help    Show this help

Environment Requirements:
  - Node.js 18+ 
  - npm/yarn
  - Internet connection (for package downloads)

Optional (for full Windows testing):
  - MSBuild (Visual Studio Build Tools)
  - Windows 10/11 SDK
  - Visual Studio 2019/2022

The integration tests will:
  1. Create a new Expo project
  2. Add Windows platform support
  3. Run prebuild to generate Windows project
  4. Validate project structure and configuration
  5. Test development workflow integration

Note: Some tests may be skipped if Windows development tools are not available.
This is normal for CI environments or non-Windows systems.
`);
}
// Run if called directly
if (require.main === module) {
    // Handle command line arguments only when run directly
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
        showHelp();
        process.exit(0);
    }
    main().catch((error) => {
        console.error('💥 Integration test runner failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=runIntegrationTests.js.map