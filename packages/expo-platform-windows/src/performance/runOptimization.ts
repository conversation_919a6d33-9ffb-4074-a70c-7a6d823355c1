#!/usr/bin/env node

/**
 * Performance Optimization Runner
 *
 * Runs comprehensive performance analysis and optimization for Windows platform
 * to ensure optimal build times, bundle sizes, and runtime performance.
 */

import * as path from 'path';

import { PerformanceOptimizer } from './optimizations';

async function main() {
  console.log('🚀 EXPO PLATFORM WINDOWS - PERFORMANCE OPTIMIZATION\n');

  // Get project root (default to current directory)
  const projectRoot = process.argv[2] || process.cwd();

  try {
    console.log(`📁 Project root: ${projectRoot}`);
    console.log('🔧 Starting performance optimization...\n');

    // Run optimization
    const optimizer = new PerformanceOptimizer(projectRoot);
    const report = await optimizer.optimizePerformance();

    // Summary
    const appliedOptimizations = report.optimizations.filter((o) => o.applied);
    const failedOptimizations = report.optimizations.filter((o) => !o.applied);
    const successRate = Math.round(
      (appliedOptimizations.length / report.optimizations.length) * 100
    );

    console.log('🎯 OPTIMIZATION RESULTS:');
    console.log(`   Total optimizations: ${report.optimizations.length}`);
    console.log(`   Applied successfully: ${appliedOptimizations.length}`);
    console.log(`   Failed: ${failedOptimizations.length}`);
    console.log(`   Success rate: ${successRate}%`);

    // Performance metrics summary
    console.log('\n📊 PERFORMANCE METRICS:');
    console.log(`   Build time: ${Math.round(report.metrics.buildTime / 1000)}s`);
    console.log(
      `   Bundle size: ${Math.round((report.metrics.bundleSize / 1024 / 1024) * 100) / 100}MB`
    );
    console.log(`   Startup time: ${Math.round(report.metrics.startupTime)}ms`);
    console.log(
      `   Memory usage: ${Math.round((report.metrics.memoryUsage / 1024 / 1024) * 100) / 100}MB`
    );

    // Recommendations
    if (report.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      report.recommendations.forEach((rec) => console.log(`   - ${rec}`));
    }

    // Failed optimizations
    if (failedOptimizations.length > 0) {
      console.log('\n❌ FAILED OPTIMIZATIONS:');
      failedOptimizations.forEach((opt) => {
        console.log(`   - ${opt.optimization}: ${opt.error}`);
      });
    }

    if (successRate >= 90) {
      console.log('\n🎉 Excellent! Performance optimization completed successfully');
    } else if (successRate >= 70) {
      console.log('\n👍 Good! Most optimizations applied successfully');
    } else {
      console.log('\n⚠️  Some optimizations failed. Review the errors above');
    }

    console.log('\n📄 Detailed report saved to PERFORMANCE_REPORT.md');
  } catch (error: any) {
    console.error('💥 Performance optimization failed:', error.message);
    process.exit(1);
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
Expo Platform Windows - Performance Optimization

Usage:
  npm run optimize                    Optimize current directory
  npm run optimize [project-path]    Optimize specific project
  npm run optimize -- --help         Show this help

The performance optimizer will:
  1. Collect performance metrics (build time, bundle size, etc.)
  2. Apply build-time optimizations (MSBuild, incremental builds)
  3. Apply bundle optimizations (tree shaking, code splitting)
  4. Apply runtime optimizations (memory, startup, rendering)
  5. Apply development optimizations (fast refresh, hot reload)
  6. Generate performance report with recommendations

Optimizations include:
  - MSBuild parallel compilation
  - Incremental builds and precompiled headers
  - Metro bundle optimization
  - Asset compression and optimization
  - Memory management improvements
  - Startup time optimization
  - Development workflow improvements

The optimizer is safe to run and will not modify your source code.
It only applies configuration optimizations and generates reports.
`);
}

// Run if called directly
if (require.main === module) {
  // Handle command line arguments only when run directly
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  main().catch((error) => {
    console.error('💥 Performance optimization failed:', error);
    process.exit(1);
  });
}

export { main as runPerformanceOptimization };
