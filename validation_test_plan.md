# External Platform System - Validation Test Plan

## Overview

This test plan validates the current external platform system implementation and confirms the gaps identified in the comprehensive analysis. The tests are designed to demonstrate the differences between built-in and external platform behavior.

## Test Environment Setup

### Prerequisites
1. Expo CLI with external platform system
2. expo-platform-windows package installed
3. Test project with external platform configuration
4. Built-in iOS/Android platforms for comparison

### Test Project Structure
```
test-project/
├── app.json (with windows platform configured)
├── package.json
├── node_modules/
│   └── expo-platform-windows/
├── ios/ (generated by prebuild)
├── android/ (generated by prebuild)
└── windows/ (generated by prebuild)
```

## Test Categories

### 1. CLI Command Parity Tests

#### Test 1.1: Run Command Behavior
**Objective**: Verify run command behavior differences between built-in and external platforms

**Test Steps**:
```bash
# Test built-in iOS run command
expo run ios --help
expo run ios --device --configuration Debug --no-build-cache

# Test external Windows run command  
expo run windows --help
expo run windows --device --configuration Debug --no-build-cache
```

**Expected Results**:
- ✅ iOS: Full Expo integration with device selection, build options, dev server integration
- ❌ Windows: React Native CLI delegation, missing Expo-specific flags, no device selection UI

**Validation Criteria**:
- [ ] Command help output identical
- [ ] All flags supported identically
- [ ] Device selection UI consistent
- [ ] Error messages consistent
- [ ] Development server integration identical

#### Test 1.2: Start Command Integration
**Objective**: Verify start command platform integration

**Test Steps**:
```bash
# Start development server
expo start

# Test keyboard shortcuts
# Press 'i' for iOS
# Press 'a' for Android  
# Press assigned key for Windows (e.g., 'w')
```

**Expected Results**:
- ✅ iOS/Android: Seamless device selection and app launching
- ❌ Windows: Fallback to run command if no device manager integration

**Validation Criteria**:
- [ ] Platform key assignment works
- [ ] Device selection UI consistent
- [ ] App launching behavior identical
- [ ] URL generation consistent
- [ ] Error handling consistent

#### Test 1.3: Prebuild Command
**Objective**: Verify prebuild functionality for external platforms

**Test Steps**:
```bash
# Test prebuild for built-in platforms
expo prebuild --platform ios --clean
expo prebuild --platform android --clean

# Test prebuild for external platform
expo prebuild --platform windows --clean
```

**Expected Results**:
- ✅ All platforms: Template application and config plugin execution
- ⚠️ Minor differences in template validation and error handling

**Validation Criteria**:
- [ ] Template discovery works
- [ ] Config plugins execute
- [ ] Native project generation successful
- [ ] Error handling consistent

#### Test 1.4: Install Command
**Objective**: Verify install command platform awareness

**Test Steps**:
```bash
# Install packages with platform-specific dependencies
expo install react-native-windows
expo install @react-native-community/slider
```

**Expected Results**:
- ✅ Built-in platforms: Platform-specific dependency resolution
- ❌ External platforms: No platform-specific logic

**Validation Criteria**:
- [ ] Platform-specific dependencies detected
- [ ] Version resolution works
- [ ] Autolinking triggered
- [ ] Installation successful

#### Test 1.5: Doctor Command
**Objective**: Verify doctor command platform validation

**Test Steps**:
```bash
# Run doctor command
expo doctor
```

**Expected Results**:
- ✅ Built-in platforms: Comprehensive health checks
- ❌ External platforms: No validation

**Validation Criteria**:
- [ ] Platform prerequisites checked
- [ ] Development environment validated
- [ ] System requirements verified
- [ ] Helpful error messages provided

### 2. Development Workflow Tests

#### Test 2.1: Metro Integration
**Objective**: Verify Metro bundler integration for external platforms

**Test Steps**:
1. Create platform-specific files:
   - `App.ios.js`
   - `App.android.js`
   - `App.windows.js`
2. Start development server
3. Verify file resolution

**Expected Results**:
- ✅ All platforms: Platform-specific file resolution works
- ✅ External platforms: Metro extensions registered correctly

**Validation Criteria**:
- [ ] Platform-specific files resolved
- [ ] Extensions registered in Metro config
- [ ] Bundling works correctly
- [ ] Hot reload functions

#### Test 2.2: Device Management
**Objective**: Compare device management between built-in and external platforms

**Test Steps**:
1. Test device discovery and selection
2. Test simulator/emulator launching
3. Test app installation and launching

**Expected Results**:
- ✅ Built-in platforms: Comprehensive device management
- ❌ External platforms: Inconsistent or missing device management

**Validation Criteria**:
- [ ] Device discovery works
- [ ] Device selection UI consistent
- [ ] Simulator launching works
- [ ] App installation successful
- [ ] Error handling consistent

### 3. Error Handling Tests

#### Test 3.1: Error Message Consistency
**Objective**: Verify error message consistency across platforms

**Test Steps**:
1. Trigger common errors (missing dependencies, invalid configuration, etc.)
2. Compare error messages between platforms

**Expected Results**:
- ✅ Built-in platforms: Consistent, helpful error messages
- ❌ External platforms: Generic or inconsistent error messages

**Validation Criteria**:
- [ ] Error message format consistent
- [ ] Error context provided
- [ ] Recovery instructions helpful
- [ ] Error codes standardized

### 4. Performance Tests

#### Test 4.1: Platform Loading Performance
**Objective**: Measure platform loading and registration performance

**Test Steps**:
1. Measure CLI startup time with external platforms
2. Measure Metro configuration time
3. Measure platform registry overhead

**Expected Results**:
- ✅ No significant performance regression

**Validation Criteria**:
- [ ] CLI startup time acceptable
- [ ] Metro config time reasonable
- [ ] Platform registry efficient

### 5. Integration Tests

#### Test 5.1: Full Development Workflow
**Objective**: Test complete development workflow for external platforms

**Test Steps**:
1. Create new project
2. Add external platform
3. Run prebuild
4. Start development server
5. Launch on platform
6. Make code changes
7. Test hot reload

**Expected Results**:
- ✅ Workflow completes successfully
- ❌ Some steps may have different behavior or require workarounds

**Validation Criteria**:
- [ ] All steps complete successfully
- [ ] No workarounds required
- [ ] Experience identical to built-in platforms

## Test Execution

### Automated Tests
```bash
# Run existing test suite
yarn test packages/@expo/cli/src/core/PlatformRegistry
yarn test packages/@expo/cli/src/start/interface
yarn test packages/@expo/metro-config/src/withExternalPlatforms

# Run integration tests
yarn test:integration external-platforms
```

### Manual Tests
1. Follow test steps outlined above
2. Document results and differences
3. Create reproduction cases for identified issues

### Performance Tests
```bash
# Measure CLI performance
time expo --help
time expo start --help

# Measure platform loading
EXPO_DEBUG=1 expo start
```

## Expected Test Results Summary

### ✅ Working Well
1. **Metro Integration**: External platforms integrate well with Metro bundler
2. **Platform Registry**: Platform discovery and registration works
3. **Key Assignment**: Smart key assignment for start interface
4. **Config Plugins**: External platform config plugins execute
5. **Template System**: Directory-based templates work

### ❌ Critical Issues
1. **Run Command**: External platforms use React Native CLI delegation
2. **Device Management**: Inconsistent device management patterns
3. **Install Integration**: No platform-specific dependency resolution
4. **Doctor Integration**: No health checking for external platforms
5. **Error Handling**: Inconsistent error messages and recovery

### ⚠️ Minor Issues
1. **Template Validation**: Different validation logic
2. **URL Generation**: Minor inconsistencies
3. **Plugin Validation**: Less robust validation

## Success Criteria

### 100% Command Parity
- [ ] All CLI commands work identically
- [ ] All flags and options supported
- [ ] Consistent error handling
- [ ] Same developer experience

### 100% Development Workflow Parity
- [ ] Seamless platform switching
- [ ] Consistent device management
- [ ] Identical URL generation
- [ ] Same keyboard shortcuts

### 100% Performance Parity
- [ ] No performance regression
- [ ] Fast platform loading
- [ ] Efficient Metro integration

## Next Steps

1. **Execute Test Plan**: Run all tests and document results
2. **Create Issue Reports**: Document specific issues with reproduction steps
3. **Prioritize Fixes**: Focus on critical issues first
4. **Implement Solutions**: Follow implementation plan to address gaps
5. **Validate Fixes**: Re-run tests to verify improvements

## Test Automation

### Continuous Integration
```yaml
# .github/workflows/external-platforms.yml
name: External Platform Tests
on: [push, pull_request]
jobs:
  test-external-platforms:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
      - name: Install dependencies
        run: yarn install
      - name: Run external platform tests
        run: yarn test:external-platforms
      - name: Run integration tests
        run: yarn test:integration
```

### Test Coverage
- Unit tests for all platform registry functionality
- Integration tests for CLI command behavior
- End-to-end tests for complete workflows
- Performance benchmarks for regression detection

This comprehensive test plan will validate the current implementation and confirm the gaps identified in the analysis, providing a solid foundation for implementing the necessary fixes to achieve 100% feature parity.
