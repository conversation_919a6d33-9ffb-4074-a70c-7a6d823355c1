#include "pch.h"
#include "App.xaml.h"
#include "MainPage.xaml.h"

using namespace winrt;
using namespace Windows::ApplicationModel;
using namespace Windows::ApplicationModel::Activation;
using namespace Windows::Foundation;
using namespace Windows::UI::Xaml;
using namespace Windows::UI::Xaml::Controls;
using namespace Windows::UI::Xaml::Navigation;
using namespace HelloWorld;
using namespace HelloWorld::implementation;

/// <summary>
/// Initializes the singleton application object.  This is the first line of authored code
/// executed, and as such is the logical equivalent of main() or WinMain().
/// </summary>
App::App() noexcept
{
#if defined _DEBUG && !defined DISABLE_XAML_GENERATED_BREAK_ON_UNHANDLED_EXCEPTION
    UnhandledException([this](IInspectable const&, UnhandledExceptionEventArgs const& e)
    {
        if (IsDebuggerPresent())
        {
            auto errorMessage = e.Message();
            __debugbreak();
        }
    });
#endif

    Suspending({ this, &App::OnSuspending });

#if defined _DEBUG
    DebugSettings().EnableFrameRateCounter(false);
#endif

    Microsoft::ReactNative::PackageProviders().Append(winrt::Microsoft::ReactNative::ReactPackageProvider());
}

/// <summary>
/// Invoked when the application is launched normally by the end user.  Other entry points
/// will be used such as when the application is launched to open a specific file.
/// </summary>
void App::OnLaunched(LaunchActivatedEventArgs const& e)
{
    auto content = Window::Current().Content();
    if (content == nullptr)
    {
        auto rootFrame = winrt::make<implementation::MainPage>();
        Window::Current().Content(rootFrame);
    }

    Window::Current().Activate();
}

/// <summary>
/// Invoked when application execution is being suspended.  Application state is saved
/// without knowing whether the application will be terminated or resumed with the contents
/// of memory still intact.
/// </summary>
void App::OnSuspending(IInspectable const&, SuspendingEventArgs const&)
{
    // Save application state and stop any background activity
}

/// <summary>
/// Invoked when Navigation to a certain page fails
/// </summary>
/// <param name="sender">The Frame which failed navigation</param>
/// <param name="e">Details about the navigation failure</param>
void App::OnNavigationFailed(IInspectable const&, NavigationFailedEventArgs const&)
{
    throw hresult_error(E_FAIL, hstring(L"Failed to load Page"));
}
